#!/usr/bin/env python3
"""
测试修复后的图片加载功能
"""

import tkinter as tk
from damage_annotation_tool import DamageAnnotationTool
import os

def test_loading():
    """测试加载功能"""
    # 创建一个隐藏的根窗口用于测试
    root = tk.Tk()
    root.withdraw()
    
    try:
        # 创建标注工具实例
        app = DamageAnnotationTool(root)
        
        # 测试加载图片文件夹
        folder_path = "不同损伤类型图片"
        if os.path.exists(folder_path):
            print(f"测试加载文件夹: {folder_path}")
            app.load_images_from_folder(folder_path)
            
            if app.image_list:
                print(f"✅ 成功找到 {len(app.image_list)} 张图片")
                
                # 测试加载第一张图片
                print("测试加载第一张图片...")
                app.load_current_image()
                
                if app.current_image is not None:
                    h, w = app.current_image.shape[:2]
                    print(f"✅ 成功加载图片，尺寸: {w}x{h}")
                    print(f"当前图片: {os.path.basename(app.current_image_path)}")
                else:
                    print("❌ 图片加载失败")
            else:
                print("❌ 未找到图片文件")
        else:
            print(f"❌ 文件夹不存在: {folder_path}")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    finally:
        root.destroy()

if __name__ == "__main__":
    print("=== 测试修复后的图片加载功能 ===\n")
    test_loading()
    print("\n=== 测试完成 ===")
    print("如果测试成功，现在可以在GUI中选择图片文件夹进行标注了！")
