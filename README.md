# 损伤标注工具 (Damage Annotation Tool)

一个专门用于标注损伤检测数据集的Python GUI工具，支持多标签标注和边界框绘制。

## 功能特点

- ✅ **多类型损伤标注**: 支持剥落、压伤、擦伤、点蚀、磨损等5种损伤类型
- ✅ **可视化标注**: 直观的边界框绘制和颜色区分
- ✅ **多标签支持**: 每张图片可以标注多个不同类型的损伤区域
- ✅ **数据导出**: 支持导出为COCO格式，便于训练深度学习模型
- ✅ **批量处理**: 支持文件夹批量加载和处理
- ✅ **标注管理**: 可以查看、编辑、删除已有标注

## 安装要求

### Python版本
- Python 3.7 或更高版本

### 依赖库
```bash
pip install -r requirements.txt
```

或手动安装：
```bash
pip install opencv-python Pillow numpy
```

## 使用方法

### 1. 启动工具
```bash
python damage_annotation_tool.py
```

### 2. 基本操作流程

1. **加载图片**
   - 点击"选择图片文件夹"按钮
   - 选择包含图片的文件夹（支持子文件夹递归搜索）
   - 支持的格式：jpg, jpeg, png, bmp, tiff, tif

2. **标注损伤**
   - 在左侧选择损伤类型（剥落、压伤、擦伤、点蚀、磨损）
   - 在图片上拖拽鼠标绘制边界框
   - 每种损伤类型有不同的颜色标识

3. **管理标注**
   - 在"当前标注"列表中查看已标注的区域
   - 选中标注项后点击"删除选中标注"可以删除
   - 使用"上一张"/"下一张"按钮切换图片

4. **保存和导出**
   - 点击"保存标注"保存为JSON格式
   - 点击"导出COCO格式"导出为COCO数据集格式

### 3. 快捷键和技巧

- **图片导航**: 使用"上一张"/"下一张"按钮快速切换
- **精确标注**: 拖拽时会显示实时预览框
- **多标签**: 同一张图片可以标注多个不同类型的损伤
- **自动保存**: 切换图片时会自动保存当前标注

## 输出格式

### JSON格式 (原始标注)
```json
{
  "image_path": {
    "annotations": [
      {
        "category": "剥落",
        "bbox": [x1, y1, x2, y2]
      }
    ]
  }
}
```

### COCO格式 (用于训练)
标准的COCO数据集格式，包含：
- `images`: 图片信息
- `annotations`: 标注信息
- `categories`: 类别定义

## 损伤类型说明

| 类型 | 颜色 | 描述 |
|------|------|------|
| 剥落 | 🔴 红色 | 表面材料脱落 |
| 压伤 | 🟢 绿色 | 受压变形损伤 |
| 擦伤 | 🔵 蓝色 | 摩擦造成的表面损伤 |
| 点蚀 | 🟡 黄色 | 局部腐蚀形成的小坑 |
| 磨损 | 🟣 紫色 | 长期使用造成的磨损 |

## 注意事项

1. **图片格式**: 建议使用高质量的TIFF或PNG格式
2. **标注精度**: 尽量准确框选损伤区域，避免包含过多背景
3. **多标签**: 如果一张图片有多种损伤，请分别标注每个区域
4. **数据备份**: 定期保存标注文件，避免数据丢失
5. **文件路径**: 避免使用包含特殊字符的文件路径

## 故障排除

### 常见问题

1. **图片无法加载**
   - 检查图片格式是否支持
   - 确认文件路径中没有特殊字符

2. **标注无法保存**
   - 检查目标文件夹的写入权限
   - 确认磁盘空间充足

3. **程序运行缓慢**
   - 大图片会自动缩放显示
   - 建议图片分辨率不超过4K

## 扩展功能

如需添加新的损伤类型，可以修改代码中的 `damage_types` 和 `damage_colors` 字典。

## 技术支持

如有问题或建议，请联系开发团队。
