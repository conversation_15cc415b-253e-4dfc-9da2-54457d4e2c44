#!/usr/bin/env python3
"""
测试图片加载功能
"""

import os
import glob
import cv2
from PIL import Image as PILImage
import numpy as np

def test_image_loading():
    """测试图片加载功能"""
    folder_path = "不同损伤类型图片"
    
    if not os.path.exists(folder_path):
        print(f"文件夹不存在: {folder_path}")
        return
    
    print(f"测试文件夹: {folder_path}")
    
    # 支持的图片格式（包含大小写变体）
    extensions = [
        '*.jpg', '*.JPG', '*.jpeg', '*.JPEG',
        '*.png', '*.PNG', '*.bmp', '*.BMP', 
        '*.tiff', '*.TIFF', '*.tif', '*.TIF'
    ]
    
    image_list = []
    
    print("正在搜索图片文件...")
    
    # 递归搜索所有子文件夹中的图片
    for ext in extensions:
        pattern = os.path.join(folder_path, "**", ext)
        found_files = glob.glob(pattern, recursive=True)
        image_list.extend(found_files)
        if found_files:
            print(f"找到 {len(found_files)} 个 {ext} 文件")
    
    # 去重并排序
    image_list = sorted(list(set(image_list)))
    print(f"\n总共找到 {len(image_list)} 张图片")
    
    if not image_list:
        print("未找到任何图片文件！")
        return
    
    # 测试加载前几张图片
    test_count = min(5, len(image_list))
    print(f"\n测试加载前 {test_count} 张图片:")
    
    for i in range(test_count):
        img_path = image_list[i]
        print(f"\n{i+1}. 测试图片: {img_path}")
        
        # 检查文件是否存在
        if not os.path.exists(img_path):
            print(f"   ❌ 文件不存在")
            continue
        
        # 获取文件大小
        file_size = os.path.getsize(img_path)
        print(f"   文件大小: {file_size} 字节")
        
        # 尝试用OpenCV加载
        try:
            img_cv = cv2.imread(img_path)
            if img_cv is not None:
                h, w, c = img_cv.shape
                print(f"   ✅ OpenCV加载成功: {w}x{h}x{c}")
            else:
                print(f"   ❌ OpenCV加载失败")
                
                # 尝试用PIL加载
                try:
                    img_pil = PILImage.open(img_path)
                    w, h = img_pil.size
                    mode = img_pil.mode
                    print(f"   ✅ PIL加载成功: {w}x{h}, 模式: {mode}")
                    
                    # 转换为numpy数组
                    img_array = np.array(img_pil)
                    print(f"   转换为数组: {img_array.shape}, dtype: {img_array.dtype}")
                    
                except Exception as e:
                    print(f"   ❌ PIL加载也失败: {str(e)}")
                    
        except Exception as e:
            print(f"   ❌ 加载异常: {str(e)}")

def main():
    print("=== 图片加载测试 ===\n")
    test_image_loading()
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    main()
