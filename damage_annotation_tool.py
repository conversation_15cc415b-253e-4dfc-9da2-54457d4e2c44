#!/usr/bin/env python3
"""
损伤标注工具 - Damage Annotation Tool
用于标注图片中的损伤区域和类型，支持多标签标注
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import numpy as np
from PIL import Image, ImageTk
import json
import os
from pathlib import Path
import glob

class DamageAnnotationTool:
    def __init__(self, root):
        self.root = root
        self.root.title("损伤标注工具 - Damage Annotation Tool")
        self.root.geometry("1400x900")
        
        # 损伤类型定义
        self.damage_types = ["剥落", "压伤", "擦伤", "点蚀", "磨损"]
        self.damage_colors = {
            "剥落": "#FF0000",  # 红色
            "压伤": "#00FF00",  # 绿色
            "擦伤": "#0000FF",  # 蓝色
            "点蚀": "#FFFF00",  # 黄色
            "磨损": "#FF00FF"   # 紫色
        }
        
        # 数据存储
        self.current_image_path = None
        self.current_image = None
        self.image_list = []
        self.current_index = 0
        self.annotations = {}  # 存储所有标注数据
        self.current_annotations = []  # 当前图片的标注
        
        # 绘制状态
        self.drawing = False
        self.start_x = 0
        self.start_y = 0
        self.current_bbox = None
        self.selected_damage_type = tk.StringVar(value=self.damage_types[0])
        
        # 缩放相关
        self.scale_factor = 1.0
        self.canvas_width = 800
        self.canvas_height = 600
        
        self.setup_ui()
        self.load_existing_annotations()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧控制面板
        control_frame = ttk.Frame(main_frame, width=300)
        control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        control_frame.pack_propagate(False)
        
        # 文件操作
        file_frame = ttk.LabelFrame(control_frame, text="文件操作", padding=10)
        file_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(file_frame, text="选择图片文件夹", command=self.select_folder).pack(fill=tk.X, pady=2)
        ttk.Button(file_frame, text="保存标注", command=self.save_annotations).pack(fill=tk.X, pady=2)
        ttk.Button(file_frame, text="导出COCO格式", command=self.export_coco).pack(fill=tk.X, pady=2)
        
        # 图片导航
        nav_frame = ttk.LabelFrame(control_frame, text="图片导航", padding=10)
        nav_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.image_info_label = ttk.Label(nav_frame, text="未加载图片")
        self.image_info_label.pack(fill=tk.X, pady=2)
        
        nav_buttons = ttk.Frame(nav_frame)
        nav_buttons.pack(fill=tk.X, pady=2)
        ttk.Button(nav_buttons, text="上一张", command=self.prev_image).pack(side=tk.LEFT, padx=2)
        ttk.Button(nav_buttons, text="下一张", command=self.next_image).pack(side=tk.RIGHT, padx=2)
        
        # 损伤类型选择
        damage_frame = ttk.LabelFrame(control_frame, text="损伤类型", padding=10)
        damage_frame.pack(fill=tk.X, pady=(0, 10))
        
        for damage_type in self.damage_types:
            color = self.damage_colors[damage_type]
            rb = ttk.Radiobutton(damage_frame, text=f"{damage_type}", 
                               variable=self.selected_damage_type, value=damage_type)
            rb.pack(anchor=tk.W, pady=2)
        
        # 当前标注列表
        annotations_frame = ttk.LabelFrame(control_frame, text="当前标注", padding=10)
        annotations_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview来显示标注
        self.annotations_tree = ttk.Treeview(annotations_frame, columns=("type", "bbox"), show="headings", height=10)
        self.annotations_tree.heading("type", text="损伤类型")
        self.annotations_tree.heading("bbox", text="边界框")
        self.annotations_tree.column("type", width=80)
        self.annotations_tree.column("bbox", width=150)
        self.annotations_tree.pack(fill=tk.BOTH, expand=True)
        
        # 删除按钮
        ttk.Button(annotations_frame, text="删除选中标注", command=self.delete_annotation).pack(fill=tk.X, pady=(5, 0))
        
        # 右侧图片显示区域
        image_frame = ttk.Frame(main_frame)
        image_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 创建Canvas用于显示图片和绘制边界框
        self.canvas = tk.Canvas(image_frame, bg="white", width=self.canvas_width, height=self.canvas_height)
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # 绑定鼠标事件
        self.canvas.bind("<Button-1>", self.start_draw)
        self.canvas.bind("<B1-Motion>", self.draw_bbox)
        self.canvas.bind("<ButtonRelease-1>", self.end_draw)
        
        # 状态栏
        self.status_label = ttk.Label(self.root, text="就绪")
        self.status_label.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=5)
        
    def select_folder(self):
        """选择包含图片的文件夹"""
        folder_path = filedialog.askdirectory(title="选择图片文件夹")
        if folder_path:
            self.load_images_from_folder(folder_path)
            
    def load_images_from_folder(self, folder_path):
        """从文件夹加载图片"""
        # 支持的图片格式
        extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.tif']
        self.image_list = []
        
        # 递归搜索所有子文件夹中的图片
        for ext in extensions:
            self.image_list.extend(glob.glob(os.path.join(folder_path, "**", ext), recursive=True))
        
        if self.image_list:
            self.current_index = 0
            self.load_current_image()
            self.status_label.config(text=f"加载了 {len(self.image_list)} 张图片")
        else:
            messagebox.showwarning("警告", "在选定文件夹中未找到图片文件")
            
    def load_current_image(self):
        """加载当前图片"""
        if not self.image_list:
            return
            
        self.current_image_path = self.image_list[self.current_index]
        
        # 使用OpenCV加载图片
        img = cv2.imread(self.current_image_path)
        if img is None:
            messagebox.showerror("错误", f"无法加载图片: {self.current_image_path}")
            return
            
        # 转换颜色空间
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        self.current_image = img
        
        # 计算缩放比例
        h, w = img.shape[:2]
        scale_w = self.canvas_width / w
        scale_h = self.canvas_height / h
        self.scale_factor = min(scale_w, scale_h, 1.0)  # 不放大，只缩小
        
        # 缩放图片
        new_w = int(w * self.scale_factor)
        new_h = int(h * self.scale_factor)
        img_resized = cv2.resize(img, (new_w, new_h))
        
        # 转换为PIL图片并显示
        pil_img = Image.fromarray(img_resized)
        self.photo = ImageTk.PhotoImage(pil_img)
        
        # 清空canvas并显示图片
        self.canvas.delete("all")
        self.canvas.create_image(0, 0, anchor=tk.NW, image=self.photo)
        
        # 加载当前图片的标注
        self.load_current_annotations()
        self.draw_all_annotations()
        
        # 更新信息显示
        filename = os.path.basename(self.current_image_path)
        self.image_info_label.config(text=f"{self.current_index + 1}/{len(self.image_list)}: {filename}")
        
    def load_current_annotations(self):
        """加载当前图片的标注"""
        if self.current_image_path in self.annotations:
            self.current_annotations = self.annotations[self.current_image_path].copy()
        else:
            self.current_annotations = []
        self.update_annotations_tree()
        
    def update_annotations_tree(self):
        """更新标注列表显示"""
        # 清空现有项目
        for item in self.annotations_tree.get_children():
            self.annotations_tree.delete(item)

        # 添加当前标注
        for i, ann in enumerate(self.current_annotations):
            bbox_str = f"({ann['bbox'][0]:.0f},{ann['bbox'][1]:.0f},{ann['bbox'][2]:.0f},{ann['bbox'][3]:.0f})"
            self.annotations_tree.insert("", "end", values=(ann['category'], bbox_str))

    def draw_all_annotations(self):
        """绘制所有已有的标注"""
        for ann in self.current_annotations:
            self.draw_annotation_bbox(ann)

    def draw_annotation_bbox(self, annotation):
        """绘制单个标注的边界框"""
        bbox = annotation['bbox']
        category = annotation['category']
        color = self.damage_colors.get(category, "#000000")

        # 将原始坐标转换为显示坐标
        x1 = bbox[0] * self.scale_factor
        y1 = bbox[1] * self.scale_factor
        x2 = bbox[2] * self.scale_factor
        y2 = bbox[3] * self.scale_factor

        # 绘制边界框
        self.canvas.create_rectangle(x1, y1, x2, y2, outline=color, width=2, tags="annotation")

        # 绘制标签
        self.canvas.create_text(x1, y1-10, text=category, fill=color, anchor=tk.SW, tags="annotation")

    def start_draw(self, event):
        """开始绘制边界框"""
        self.drawing = True
        self.start_x = event.x
        self.start_y = event.y

        # 删除之前的临时边界框
        self.canvas.delete("temp_bbox")

    def draw_bbox(self, event):
        """绘制边界框过程中"""
        if self.drawing:
            # 删除之前的临时边界框
            self.canvas.delete("temp_bbox")

            # 绘制新的临时边界框
            color = self.damage_colors.get(self.selected_damage_type.get(), "#000000")
            self.canvas.create_rectangle(self.start_x, self.start_y, event.x, event.y,
                                       outline=color, width=2, tags="temp_bbox")

    def end_draw(self, event):
        """结束绘制边界框"""
        if self.drawing:
            self.drawing = False

            # 删除临时边界框
            self.canvas.delete("temp_bbox")

            # 计算边界框坐标（确保左上角和右下角正确）
            x1 = min(self.start_x, event.x)
            y1 = min(self.start_y, event.y)
            x2 = max(self.start_x, event.x)
            y2 = max(self.start_y, event.y)

            # 检查边界框大小
            if abs(x2 - x1) < 5 or abs(y2 - y1) < 5:
                return  # 太小的框不保存

            # 转换为原始图片坐标
            orig_x1 = x1 / self.scale_factor
            orig_y1 = y1 / self.scale_factor
            orig_x2 = x2 / self.scale_factor
            orig_y2 = y2 / self.scale_factor

            # 创建标注
            annotation = {
                'category': self.selected_damage_type.get(),
                'bbox': [orig_x1, orig_y1, orig_x2, orig_y2]
            }

            # 添加到当前标注列表
            self.current_annotations.append(annotation)

            # 重新绘制所有标注
            self.canvas.delete("annotation")
            self.draw_all_annotations()

            # 更新标注列表显示
            self.update_annotations_tree()

    def delete_annotation(self):
        """删除选中的标注"""
        selection = self.annotations_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的标注")
            return

        # 获取选中项的索引
        item = selection[0]
        index = self.annotations_tree.index(item)

        # 删除标注
        if 0 <= index < len(self.current_annotations):
            del self.current_annotations[index]

            # 重新绘制
            self.canvas.delete("annotation")
            self.draw_all_annotations()

            # 更新列表显示
            self.update_annotations_tree()

    def prev_image(self):
        """上一张图片"""
        if not self.image_list:
            return

        # 保存当前标注
        self.save_current_annotations()

        # 切换到上一张
        self.current_index = (self.current_index - 1) % len(self.image_list)
        self.load_current_image()

    def next_image(self):
        """下一张图片"""
        if not self.image_list:
            return

        # 保存当前标注
        self.save_current_annotations()

        # 切换到下一张
        self.current_index = (self.current_index + 1) % len(self.image_list)
        self.load_current_image()

    def save_current_annotations(self):
        """保存当前图片的标注"""
        if self.current_image_path:
            self.annotations[self.current_image_path] = self.current_annotations.copy()

    def save_annotations(self):
        """保存所有标注到文件"""
        if not self.annotations and not self.current_annotations:
            messagebox.showwarning("警告", "没有标注数据可保存")
            return

        # 保存当前图片的标注
        self.save_current_annotations()

        # 选择保存位置
        filename = filedialog.asksaveasfilename(
            title="保存标注文件",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.annotations, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("成功", f"标注已保存到: {filename}")
                self.status_label.config(text=f"标注已保存: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"保存失败: {str(e)}")

    def load_existing_annotations(self):
        """加载已有的标注文件"""
        # 可以在这里添加自动加载逻辑
        pass

    def export_coco(self):
        """导出为COCO格式"""
        if not self.annotations and not self.current_annotations:
            messagebox.showwarning("警告", "没有标注数据可导出")
            return

        # 保存当前标注
        self.save_current_annotations()

        # 选择导出位置
        filename = filedialog.asksaveasfilename(
            title="导出COCO格式",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                coco_data = self.convert_to_coco_format()
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(coco_data, f, ensure_ascii=False, indent=2)
                messagebox.showinfo("成功", f"COCO格式数据已导出到: {filename}")
                self.status_label.config(text=f"COCO数据已导出: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {str(e)}")

    def convert_to_coco_format(self):
        """转换为COCO格式"""
        # COCO格式结构
        coco_data = {
            "info": {
                "description": "Damage Detection Dataset",
                "version": "1.0",
                "year": 2024,
                "contributor": "Damage Annotation Tool",
                "date_created": "2024-01-01"
            },
            "licenses": [],
            "images": [],
            "annotations": [],
            "categories": []
        }

        # 添加类别
        for i, damage_type in enumerate(self.damage_types):
            coco_data["categories"].append({
                "id": i + 1,
                "name": damage_type,
                "supercategory": "damage"
            })

        # 创建类别名称到ID的映射
        category_name_to_id = {name: i + 1 for i, name in enumerate(self.damage_types)}

        annotation_id = 1

        # 处理每张图片
        for image_id, (image_path, annotations) in enumerate(self.annotations.items(), 1):
            # 获取图片信息
            img = cv2.imread(image_path)
            if img is None:
                continue

            height, width = img.shape[:2]
            filename = os.path.basename(image_path)

            # 添加图片信息
            coco_data["images"].append({
                "id": image_id,
                "width": width,
                "height": height,
                "file_name": filename
            })

            # 添加标注
            for ann in annotations:
                bbox = ann['bbox']
                category_name = ann['category']

                # 转换边界框格式 (x1,y1,x2,y2) -> (x,y,width,height)
                x = bbox[0]
                y = bbox[1]
                w = bbox[2] - bbox[0]
                h = bbox[3] - bbox[1]

                coco_data["annotations"].append({
                    "id": annotation_id,
                    "image_id": image_id,
                    "category_id": category_name_to_id[category_name],
                    "bbox": [x, y, w, h],
                    "area": w * h,
                    "iscrowd": 0
                })

                annotation_id += 1

        return coco_data

    def load_annotations_file(self):
        """加载标注文件"""
        filename = filedialog.askopenfilename(
            title="加载标注文件",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.annotations = json.load(f)

                # 如果当前有图片，重新加载标注
                if self.current_image_path:
                    self.load_current_annotations()
                    self.canvas.delete("annotation")
                    self.draw_all_annotations()

                messagebox.showinfo("成功", f"标注文件已加载: {filename}")
                self.status_label.config(text=f"标注文件已加载: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"加载失败: {str(e)}")


def main():
    """主程序入口"""
    root = tk.Tk()
    app = DamageAnnotationTool(root)

    # 添加菜单栏
    menubar = tk.Menu(root)
    root.config(menu=menubar)

    # 文件菜单
    file_menu = tk.Menu(menubar, tearoff=0)
    menubar.add_cascade(label="文件", menu=file_menu)
    file_menu.add_command(label="选择图片文件夹", command=app.select_folder)
    file_menu.add_separator()
    file_menu.add_command(label="加载标注文件", command=app.load_annotations_file)
    file_menu.add_command(label="保存标注", command=app.save_annotations)
    file_menu.add_separator()
    file_menu.add_command(label="导出COCO格式", command=app.export_coco)
    file_menu.add_separator()
    file_menu.add_command(label="退出", command=root.quit)

    # 帮助菜单
    help_menu = tk.Menu(menubar, tearoff=0)
    menubar.add_cascade(label="帮助", menu=help_menu)
    help_menu.add_command(label="使用说明", command=lambda: messagebox.showinfo(
        "使用说明",
        "1. 点击'选择图片文件夹'加载图片\n"
        "2. 选择损伤类型\n"
        "3. 在图片上拖拽鼠标绘制边界框\n"
        "4. 使用'上一张'/'下一张'切换图片\n"
        "5. 点击'保存标注'保存工作\n"
        "6. 点击'导出COCO格式'导出训练数据"
    ))

    root.mainloop()


if __name__ == "__main__":
    main()
